<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Test Environment - QuoteAI</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .test-header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 1rem 0;
            color: #374151;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .test-link {
            display: block;
            padding: 1rem;
            background: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s ease;
        }
        
        .test-link:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-link-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .test-link-desc {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .test-url {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .instructions h4 {
            margin: 0 0 0.5rem 0;
            color: #1e40af;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
            color: #1e40af;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-dev {
            background: #f59e0b;
        }
        
        .status-prod {
            background: #10b981;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Portal Test Environment</h1>
            <p>Test the new QuoteAI portal mode with different tradie names and configurations</p>
        </div>
        
        <div class="test-content">
            <div class="instructions">
                <h4>📋 Testing Instructions</h4>
                <ul>
                    <li>Click on any test link below to open the portal in a new tab</li>
                    <li>Test different tradie names to see URL parameter extraction</li>
                    <li>Try disconnecting your internet to test offline mode</li>
                    <li>Test on different screen sizes (mobile, tablet, desktop)</li>
                    <li>Check browser console for any errors or warnings</li>
                </ul>
            </div>
            
            <div class="test-section">
                <h3>🔧 Development Environment Tests</h3>
                <p>These links use the development server (localhost:5173) for testing during development.</p>
                
                <div class="test-links">
                    <a href="/dist/portal-dev.html?tradie=aquaflow-plumbers" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-dev"></span>
                            AquaFlow Plumbers
                        </div>
                        <div class="test-link-desc">Test with a plumbing business name</div>
                        <div class="test-url">localhost:5173/dist/portal-dev.html?tradie=aquaflow-plumbers</div>
                    </a>
                    
                    <a href="/dist/portal-dev.html?tradie=sparky-electrical" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-dev"></span>
                            Sparky Electrical
                        </div>
                        <div class="test-link-desc">Test with an electrical business name</div>
                        <div class="test-url">localhost:5173/dist/portal-dev.html?tradie=sparky-electrical</div>
                    </a>

                    <a href="/dist/portal-dev.html?tradie=handy-home-repairs" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-dev"></span>
                            Handy Home Repairs
                        </div>
                        <div class="test-link-desc">Test with a general handyman business</div>
                        <div class="test-url">localhost:5173/dist/portal-dev.html?tradie=handy-home-repairs</div>
                    </a>

                    <a href="/dist/portal-dev.html?tradie=roof-masters" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-dev"></span>
                            Roof Masters
                        </div>
                        <div class="test-link-desc">Test with a roofing business name</div>
                        <div class="test-url">localhost:5173/dist/portal-dev.html?tradie=roof-masters</div>
                    </a>

                    <a href="/dist/portal-dev.html" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-dev"></span>
                            Empty Tradie Name
                        </div>
                        <div class="test-link-desc">Test error handling with missing tradie name</div>
                        <div class="test-url">localhost:5173/dist/portal-dev.html</div>
                    </a>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🌐 Production Environment Tests</h3>
                <p>These links simulate the production environment (quoteai.com) for final testing.</p>
                
                <div class="test-links">
                    <a href="https://quoteai.com/chat/aquaflow-plumbers" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-prod"></span>
                            Production - AquaFlow Plumbers
                        </div>
                        <div class="test-link-desc">Test production deployment</div>
                        <div class="test-url">quoteai.com.au/chat/aquaflow-plumbers</div>
                    </a>
                    
                    <a href="https://quoteai.com/chat/sparky-electrical" target="_blank" class="test-link">
                        <div class="test-link-title">
                            <span class="status-indicator status-prod"></span>
                            Production - Sparky Electrical
                        </div>
                        <div class="test-link-desc">Test production deployment</div>
                        <div class="test-url">quoteai.com.au/chat/sparky-electrical</div>
                    </a>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🔍 Testing Checklist</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div>
                        <h4>✅ Functionality Tests</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li>☐ Portal loads correctly</li>
                            <li>☐ Tradie name displays properly</li>
                            <li>☐ Chat functionality works</li>
                            <li>☐ Image upload works</li>
                            <li>☐ Connection status updates</li>
                            <li>☐ Error handling works</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4>📱 Responsive Tests</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li>☐ Desktop (1200px+)</li>
                            <li>☐ Tablet (768px-1199px)</li>
                            <li>☐ Mobile (320px-767px)</li>
                            <li>☐ Landscape orientation</li>
                            <li>☐ Portrait orientation</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4>🌐 Browser Tests</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li>☐ Chrome</li>
                            <li>☐ Firefox</li>
                            <li>☐ Safari</li>
                            <li>☐ Edge</li>
                            <li>☐ Mobile browsers</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4>⚡ Performance Tests</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li>☐ Fast loading times</li>
                            <li>☐ Smooth animations</li>
                            <li>☐ No memory leaks</li>
                            <li>☐ Offline functionality</li>
                            <li>☐ Error recovery</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive functionality to the test page
        document.addEventListener('DOMContentLoaded', () => {
            // Add click tracking for test links
            const testLinks = document.querySelectorAll('.test-link');
            testLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    console.log('Testing portal:', link.href);
                    
                    // Add visual feedback
                    link.style.background = '#e0e7ff';
                    setTimeout(() => {
                        link.style.background = '';
                    }, 200);
                });
            });
            
            // Add keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    window.close();
                }
            });
        });
    </script>
</body>
</html>
