/**
 * Common types used throughout the application
 */

export interface Suggestion {
  text: string;
  value: string;
}

export interface Message {
  type: 'user' | 'bot';
  content: string;
  attachments?: string[]; // URLs to attached images
  timestamp?: number;
  suggestions?: Suggestion[]; // Optional suggestions for next messages
}

export interface ChatWidgetProps {
  apiKey: string;           // API key for authentication
  websiteUrl?: string;      // Website URL for domain verification (optional, will use hostname if not provided)
  apiUrl?: string;          // Optional API URL override
  isPortalMode?: boolean;   // Portal mode - chat is always open, no floating button
  tradieName?: string;      // Tradie name for portal mode header display
  // Legacy props for backward compatibility
  clientId?: string;        // Deprecated: Use websiteUrl instead
  uuid?: string;            // Deprecated: Use websiteUrl instead
}

export interface ImagePreview {
  url: string;
  file: File;
}

export interface ExpandedImageState {
  isOpen: boolean;
  url: string | null;
}
